<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏中心</title>
    <link rel="stylesheet" href="css/common.css">
    <style>
        /* 游戏中心页面特有样式 */
        .hero {
            padding: 140px 0 60px;
            margin-top: 80px;
        }

        .hero h2 {
            font-size: 3.5rem;
            margin-bottom: 20px;
        }

        .hero p {
            font-size: 1.125rem;
            margin-bottom: 40px;
            max-width: 480px;
        }

        .main-content {
            margin: 40px 0 0 0;
        }

        /* 游戏网格布局 */
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 32px;
            padding: 60px 0;
            position: relative;
            z-index: 1;
        }

        /* 游戏卡片样式 */
        .game-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .game-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 119, 48, 0.3);
            background: rgba(255, 255, 255, 0.08);
        }

        .game-image {
            width: 120px;
            height: 120px;
            border-radius: 12px;
            overflow: hidden;
            flex-shrink: 0;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .game-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .game-card:hover .game-image img {
            transform: scale(1.05);
        }

        .game-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;
            height: 100%;
        }

        .game-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: white;
            margin: 0;
            line-height: 1.3;
            letter-spacing: -0.01em;
        }

        .game-meta {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-bottom: 8px;
        }

        .game-category {
            background: linear-gradient(135deg, #7877c6 0%, #ff7730 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .game-age {
            background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .game-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            line-height: 1.6;
            margin: 8px 0;
            flex: 1;
            font-weight: 400;
        }

        .game-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-top: auto;
            padding-top: 16px;
        }

        .download-btn {
            background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%);
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            border: 1px solid rgba(255, 119, 48, 0.3);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.3);
        }

        .detail-btn {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: inline-block;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .detail-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
        }

        /* 游戏页面特有样式 */
        .main-content {
            padding: 60px 0;
        }

        /* 分类筛选 */
        .filter-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            padding: 32px;
            border-radius: 16px;
            margin-bottom: 40px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 10px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            color: white;
            border-color: rgba(255, 119, 48, 0.3);
            transform: translateY(-1px);
        }

        /* 游戏网格布局 */
        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 32px;
            padding: 60px 0;
            position: relative;
            z-index: 1;
        }

        /* 游戏卡片样式 */
        .game-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .game-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 119, 48, 0.3);
            background: rgba(255, 255, 255, 0.08);
        }

        .game-image {
            width: 120px;
            height: 120px;
            border-radius: 12px;
            overflow: hidden;
            flex-shrink: 0;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .game-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .game-card:hover .game-image img {
            transform: scale(1.05);
        }

        .game-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;
            height: 100%;
        }

        .game-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: white;
            margin: 0;
            line-height: 1.3;
            letter-spacing: -0.01em;
        }

        .game-meta {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-bottom: 8px;
        }

        .game-category {
            background: linear-gradient(135deg, #7877c6 0%, #ff7730 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .game-age {
            background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .game-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            line-height: 1.6;
            margin: 8px 0;
            flex: 1;
            font-weight: 400;
        }

        .game-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            margin-top: auto;
            padding-top: 16px;
        }

        .download-btn {
            background: linear-gradient(135deg, #ff7730 0%, #ff4757 100%);
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
            border: 1px solid rgba(255, 119, 48, 0.3);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.3);
        }

        .detail-btn {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            padding: 10px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: inline-block;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .detail-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
        }

        /* 分类筛选 */
        .filter-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            padding: 32px;
            border-radius: 16px;
            margin-bottom: 40px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 10px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            color: white;
            border-color: rgba(255, 119, 48, 0.3);
            transform: translateY(-1px);
        }

        /* 页脚样式 */
        .footer {
            background: rgba(10, 10, 10, 0.9);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
            padding: 60px 0 30px;
            margin-top: 80px;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 119, 48, 0.03) 0%, rgba(120, 119, 198, 0.03) 100%);
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            position: relative;
            z-index: 1;
        }

        .footer-section h4 {
            font-size: 1.125rem;
            margin-bottom: 20px;
            color: white;
            position: relative;
            padding-bottom: 12px;
            font-weight: 700;
            letter-spacing: -0.01em;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, #ff7730, #7877c6);
            border-radius: 1px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 12px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: inline-block;
            font-weight: 400;
        }

        .footer-section ul li a:hover {
            color: #ff7730;
            transform: translateX(4px);
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: rgba(255, 255, 255, 0.7);
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .contact-info:hover {
            color: white;
            transform: translateX(4px);
        }

        .contact-info i {
            margin-right: 12px;
            color: #ff7730;
            font-size: 1rem;
            width: 18px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.5);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin-bottom: 8px;
            font-size: 0.8rem;
            line-height: 1.5;
            font-weight: 400;
        }

        .footer-bottom a {
            color: rgba(255, 255, 255, 0.7);
            transition: color 0.3s ease;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            color: #ff7730;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 16px;
            }

            .hero {
                padding: 120px 0 50px;
            }

            .hero h2 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .games-grid {
                grid-template-columns: 1fr;
                gap: 24px;
                padding: 40px 0;
            }

            .game-card {
                flex-direction: column;
                text-align: center;
                padding: 20px;
            }

            .game-image {
                width: 100%;
                height: 200px;
                align-self: center;
            }

            .filter-section {
                padding: 24px;
            }

            .filter-buttons {
                gap: 8px;
            }

            .filter-btn {
                padding: 8px 16px;
                font-size: 0.8rem;
            }

            .nav-links {
                gap: 24px;
            }

            .nav-auth {
                gap: 12px;
            }

            .nav-auth a,
            .user-btns a {
                padding: 8px 16px;
                font-size: 13px;
            }

            .footer {
                padding: 40px 0 20px;
            }

            .footer-content {
                gap: 32px;
            }
        }

        @media (max-width: 480px) {
            .hero h2 {
                font-size: 2rem;
            }

            .nav-container {
                flex-direction: column;
                height: auto;
                gap: 16px;
                padding: 16px 0;
            }

            .nav-links {
                gap: 16px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .nav-auth {
                gap: 8px;
            }

            .game-card {
                padding: 16px;
            }

            .filter-section {
                padding: 20px;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header" id="header">
        <div class="container nav-container">
            <div class="logo">
                <img src="img/logo.png" height="40" alt="Logo" />
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html" class="active">游戏中心</a>
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
            </div>
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 游戏中心标题区域 -->
    <div class="hero">
        <div class="container">
            <h2>🎮 游戏中心</h2>
            <p>探索精彩游戏世界，发现你的下一个最爱</p>
        </div>
    </div>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">

            <!-- 游戏分类筛选 -->
            <!-- <section class="filter-section">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">游戏分类</h3>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-category="all">全部游戏</button>
                    <button class="filter-btn" data-category="战略">战略策略</button>
                    <button class="filter-btn" data-category="角色扮演">角色扮演</button>
                    <button class="filter-btn" data-category="休闲">休闲益智</button>
                    <button class="filter-btn" data-category="动作">动作冒险</button>
                    <button class="filter-btn" data-category="竞技">竞技体育</button>
                </div>
            </section> -->

            <!-- 游戏列表 -->
            <section class="games-grid" id="gamesContainer">
                <!-- 游戏卡片将通过JavaScript动态生成 -->
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <!-- 脚本 -->
    <script src="js/login.js"></script>
    <script src="js/common.js"></script>
    <script>
        // 游戏数据
        const gamesList = [
            {
                id: 'g1',
                name: '诸侯征战',
                image: 'img/app/1/icon.png',
                images: [
                    'img/app/1/d1.jpg',
                    'img/app/1/d2.jpg',
                    'img/app/1/d3.jpg',
                    'img/app/1/d4.jpg',
                ],
                category: '战略',
                ageRating: '全年龄段',
                description: '一款复古纯粹的模拟经营三国手游。多人合作，疯狂国战，只有最好的策略才能赢得国战。大王快点来吹起集结号，奏响三国战歌！与兄弟们打下第一战国吧！ 在这里，您可以与朋友团结合作，统一指挥，千人国战；在这里，您需要警惕虎视眈眈的邻居，保卫自己的资源；在这里，您可以低调经营自己的城池，享受成长的满足；在这里，也可以纵横捭阖，领略与人争斗的其乐无穷。',
                features: [],
                publisher: '广州市勤众网络科技有限公司',
                supplier: '广州市勤众网络科技有限公司',
                contactPhone: '18715777095',
                downloadUrl: 'https://cdn.910app.com/android/downloads/zhzz-7750001-20250107164317.apk'
            },
            {
                id: 'g2',
                name: '朕的江山2',
                image: 'img/app/2/icon.png',
                images: [
                    'img/app/2/d1.jpg',
                    'img/app/2/d2.jpg',
                    'img/app/2/d3.jpg',
                    'img/app/2/d4.jpg',
                ],
                category: '战略',
                ageRating: '全年龄段',
                description: '《朕的江山2》是一款经典三国战役SLG手游。连城式大地图还原真实的三国战场，全服玩家同时征伐战场冲锋陷阵，四大兵种三类将领互相制衡，任君招募的上白名历史名将，高能的策略排兵布阵方能赢得国战，一统江山，战鼓齐鸣一起书写新的三国激战历史！',
                features: [],
                publisher: '广州立早网络科技有限公司',
                supplier: '广州立早网络科技有限公司',
                contactPhone: '020-38353818',
                downloadUrl: 'https://cdn.910app.com/android/downloads/zdjs2-60040040-20250325151214.apk'
            },
            {
                id: 'g3',
                name: '不朽大陆 ',
                image: 'img/app/3/icon.png',
                images: [
                    'img/app/3/d1.jpg',
                    'img/app/3/d2.jpg',
                    'img/app/3/d3.jpg',
                    'img/app/3/d4.jpg',
                ],
                category: '卡牌',
                ageRating: '全年龄段',
                description: '不朽大陆官方版是一款美漫式放置卡牌游戏，在故事背景中一场突如其来的未知病毒，爆发了外星人入侵的事实，为了阻止这场浩劫，你被选中为指挥官，现在你需要带领其他组织成员，寻找病毒源头并解放城市。',
                features: [],
                publisher: '武汉手盟网络科技股份有限公司',
                supplier: '武汉手盟网络科技股份有限公司',
                contactPhone: '027-85314686',
                downloadUrl: 'https://cdn.910app.com/android/downloads/bxdl-63560001-20250427120034.apk'
            },
            {
                id: 'g4',
                name: '攻城掠地',
                image: 'img/app/4/icon.png',
                images: [
                    'img/app/4/d1.jpg',
                    'img/app/4/d2.jpg',
                    'img/app/4/d3.jpg',
                    'img/app/4/d4.jpg',
                ],
                category: '策略',
                ageRating: '全年龄段',
                description: '《攻城掠地》是一款强调“国战”的战争策略游戏，本作由《傲视天地》原班人马创新打造，秉承经典，推陈出新。实景还原三国战图，包含多达300个关隘城池，开创全景即时国战!文臣武将齐数登场，打破“重武轻文”的游戏桎梏，内政事务、军师上阵、计策绝技演绎文官真本色!其次，递进掩杀式即时战斗模式、地形系统的引入，以及四大资源、战术、兵器、科技、皇城官职战等丰富的特色玩法，让你充分自由的享受鼎足争雄的热血，圆满开疆扩土、统一天下的宏梦!',
                features: [],
                publisher: '上海手盟网络科技有限公司',
                supplier: '上海手盟网络科技有限公司',
                contactPhone: '020-38204141',
                downloadUrl: 'https://cdn.910app.com/android/downloads/gcld.apk'
            },
            {
                id: 'g5',
                name: '割据天下',
                image: 'img/app/5/icon.png',
                images: [
                    'img/app/5/d1.jpg',
                    'img/app/5/d2.jpg',
                    'img/app/5/d3.jpg',
                    'img/app/5/d4.jpg',
                ],
                category: '战略',
                ageRating: '全年龄段',
                description: ' 《割据天下》是一款文明题材策略战争手游。游戏内设计了宏伟的战争场面，多元文化和不同种族的热血碰撞，你将亲自上阵，巧用天时地利人和，创立基业，组建联盟、自由扩张，智取天下，谱写属于你的传奇时代。',
                features: [],
                publisher: '武汉狙击手网络科技有限公司',
                supplier: '武汉狙击手网络科技有限公司',
                contactPhone: '18995622586',
                downloadUrl: 'https://cdn.910app.com/android/downloads/gjtx-67240001-20240510104416.apk'
            },
            {
                id: 'g6',
                name: '帝国霸权',
                image: 'img/app/6/icon.png',
                images: [
                    'img/app/6/d1.jpg',
                    'img/app/6/d2.jpg',
                    'img/app/6/d3.jpg',
                    'img/app/6/d4.jpg',
                ],
                category: '战略',
                ageRating: '16+',
                description: '帝国霸权是一款以中世纪时期罗马帝国为原型打造slg手游，玩家可以培养招募兵马训练将领，玩家将在游戏找那个建造自己的专属帝国，领袖意识，拥有多种不同种族的士兵，统帅一支强大的军队',
                features: [],
                publisher: '上海手盟网络科技有限公司',
                supplier: '上海手盟网络科技有限公司',
                contactPhone: '020-38204141',
                downloadUrl: 'https://cdn.910app.com/android/downloads/dgbq-24310040-20220629114329.apk'
            },
        ];

        // 动态生成游戏卡片
        function renderGames(games = gamesList) {
            const gamesContainer = document.getElementById('gamesContainer');
            gamesContainer.innerHTML = '';
            
            games.forEach(game => {
                const gameCard = document.createElement('div');
                gameCard.className = 'game-card';
                gameCard.innerHTML = `
                    <div class="game-image">
                        <img src="${game.image}" alt="${game.name}">
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">${game.name}</h3>
                        <div class="game-meta">
                            <span class="game-category">${game.category}</span>
                            <span class="game-age">${game.ageRating}</span>
                        </div>
                        <p class="game-description">${game.description}</p>
                        <div class="game-actions">
                            <a href="${game.downloadUrl}" class="download-btn">
                                <i class="fas fa-download"></i>
                                游戏下载
                            </a>
                            <a href="product-detail.html?id=${game.id}" class="detail-btn">
                                查看详情
                            </a>
                        </div>
                    </div>
                `;
                gamesContainer.appendChild(gameCard);
            });
        }

        // 分类筛选功能
        function initCategoryFilter() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            
            filterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // 移除所有按钮的激活状态
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // 激活当前按钮
                    button.classList.add('active');
                    
                    const category = button.dataset.category;
                    
                    if (category === 'all') {
                        renderGames(gamesList);
                    } else {
                        const filteredGames = gamesList.filter(game => game.category === category);
                        renderGames(filteredGames);
                    }
                });
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            renderGames();
            initCategoryFilter();
        });
    </script>
</body>
</html> 