<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系我们 - 小程序开发</title>
    <style>
        /* 复用基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
        }
        
        /* 容器样式 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* 头部和导航样式 */
        .header {
            background-color: #2196F3;
            padding: 20px;
            color: white;
        }
        
        .nav {
            background-color: #333;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .nav-container {
            display: flex;
            justify-content: flex-end; /* 改为右对齐 */
            align-items: center;
            height: 50px; /* 固定高度 */
        }
        .nav-links {
            display: flex;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 30px;
            padding: 5px 0;
            position: relative;
        }
        
        .nav-links a:hover::after,
        .nav-links a.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #2196F3;
        }
        
        .nav-links a.active {
            color: #2196F3;
        }
        .nav-auth {
            margin-right: auto;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .nav-auth a {
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
            margin-left: 15px;
        }
        .nav-auth a:first-child {
            margin-left: 0;
        }
        .nav-auth a::after {
            display: none;
        }
        .nav-auth .login-btn {
            color: #2196F3;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .nav-auth .login-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .nav-auth .register-btn {
            background-color: #2196F3;
            color: white;
        }
        .nav-auth .register-btn:hover {
            background-color: #1976D2;
        }
        .nav-auth i {
            margin-right: 4px;
        }
        
        /* 页脚样式 */
        .footer {
            background-color: #333;
            color: white;
            padding: 50px 0 20px;
            margin-top: 60px;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-bottom: 30px;
        }
        
        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 25px;
            color: #2196F3;
            position: relative;
            padding-bottom: 10px;
        }
        
        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background-color: #2196F3;
        }
        
        .footer-section ul {
            list-style: none;
        }
        
        .footer-section ul li {
            margin-bottom: 12px;
        }
        
        .footer-section ul li a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s;
            opacity: 0.8;
        }
        
        .footer-section ul li a:hover {
            color: #2196F3;
            opacity: 1;
        }
        
        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            opacity: 0.8;
            transition: opacity 0.3s;
        }
        
        .contact-info:hover {
            opacity: 1;
        }
        
        .contact-info i {
            margin-right: 12px;
            color: #2196F3;
            font-size: 1.1em;
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
        }
        
        /* 联系我们特定样式 */
        .contact-hero {
            text-align: center;
            padding: 50px 0;
            background-color: #f9f9f9;
            margin-bottom: 40px;
        }
        
        .contact-hero h2 {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 20px;
        }
        
        .contact-hero p {
            color: #666;
            font-size: 1.2em;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .contact-grid {
            max-width: 800px;
            margin: 40px auto;
        }
        
        .contact-info-card {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
        }
        
        .contact-info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 30px;
            padding: 25px;
            background: #f9f9f9;
            border-radius: 10px;
            transition: transform 0.3s;
        }
        
        .contact-info-item:last-child {
            margin-bottom: 0;
        }
        
        .contact-info-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .contact-info-item i {
            font-size: 2em;
            color: #2196F3;
            margin-right: 20px;
            margin-top: 5px;
        }
        
        .contact-info-content {
            flex: 1;
        }
        
        .contact-info-item h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.4em;
        }
        
        .contact-info-item p {
            color: #666;
            line-height: 1.6;
            font-size: 1.1em;
        }
        
        .map-container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            height: 100%;
        }
        
        .map-container iframe {
            width: 100%;
            height: 100%;
            min-height: 400px;
            border: none;
            border-radius: 10px;
        }

                /* 发布按钮样式 */
                .publish-btn {
            background-color: #2196F3;
            color: white !important;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        
        .publish-btn:hover {
            background-color: #1976D2;
            transform: translateY(-2px);
        }
        
        .logout-btn {
            color: #fff !important;
            background-color: rgba(255, 255, 255, 0.1);
            margin-left: 15px;
            display: flex;
            align-items: center;
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .user-btns {
            display: flex;
            align-items: center;
        }
        
        .user-btns a {
            text-decoration: none;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .user-btns i {
            margin-right: 6px;
            font-size: 14px;
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="100" /></h1>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">APP开发</a>
                <a href="products-wx.html">小程序开发</a>
                <a href="products-soft.html">管理软件开发</a>
                <!-- <a href="solutions.html">解决方案</a> -->
                <!-- <a href="cases.html">成功案例</a> -->
                <a href="about.html">关于我们</a>
                <a href="contact.html" class="active">联系我们</a>
            </div>
        </div>
    </div>

    <!-- 英雄区域 -->
    <div class="contact-hero">
        <div class="container">
            <h2>联系我们</h2>
            <p>随时欢迎您的咨询，我们将为您提供专业的服务</p>
        </div>
    </div>

    <div class="container">
        <div class="contact-grid">
            <!-- 联系信息 -->
            <div class="contact-info-card">
                <div class="contact-info-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <div class="contact-info-content">
                        <h3>公司地址</h3>
                        <p>广州市天河区黄埔大道中660号之-2301、2302房</p>
                    </div>
                </div>
                <div class="contact-info-item">
                    <i class="fas fa-phone"></i>
                    <div class="contact-info-content">
                        <h3>联系电话</h3>
                        <p>020-38383518</p>
                    </div>
                </div>
                <!-- <div class="contact-info-item">
                    <i class="fas fa-envelope"></i>
                    <div class="contact-info-content">
                        <h3>电子邮箱</h3>
                        <p><EMAIL><br><EMAIL></p>
                    </div>
                </div> -->
                <div class="contact-info-item">
                    <i class="fas fa-clock"></i>
                    <div class="contact-info-content">
                        <h3>工作时间</h3>
                        <p>周一至周五: 9:00 - 18:00<br>周末: 休息</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">APP开发</a></li>
                        <li><a href="products-wx.html">小程序开发</a></li>
                        <li><a href="products-soft.html">管理软件开发</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                        <li><a href="contact.html">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 GuangZhouLiZao. All rights reserved.</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>
    <script src="js/login.js"></script>
</body>
</html> 