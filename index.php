<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
define('USER_FILE', '__users.json');
if (!file_exists(USER_FILE)) {
    file_put_contents(USER_FILE, json_encode([]));
}
function handleRequest() {
    $method = $_SERVER['REQUEST_METHOD'];
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    switch ($action) {
        case 'register':
            return register();
        case 'login':
            return login();
        case 'publish':
            echo json_encode(['success' => true]);
            die;
        default:
            return ['success' => false, 'message' => '未知操作'];
    }
}

// 注册函数
function register() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        return ['success' => false, 'message' => '只支持POST请求'];
    }
    $username = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    if (empty($username) || empty($password)) {
        return ['success' => false, 'message' => '用户名和密码不能为空'];
    }
    if (strlen($password) < 6) {
        return ['success' => false, 'message' => '密码长度不能少于6个字符'];
    }
    $users = json_decode(file_get_contents(USER_FILE), true);
    foreach ($users as $user) {
        if ($user['username'] === $username) {
            return ['success' => false, 'message' => '用户名已存在'];
        }
    }
    $users[] = [
        'username' => $username,
        'password' => password_hash($password, PASSWORD_DEFAULT), // 安全地存储密码
        'created_at' => date('Y-m-d H:i:s')
    ];
    file_put_contents(USER_FILE, json_encode($users, JSON_PRETTY_PRINT));
    
    return ['success' => true, 'message' => '注册成功'];
}

// 登录函数
function login() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        return ['success' => false, 'message' => '只支持POST请求'];
    }
    $username = isset($_POST['phone']) ? trim($_POST['phone']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    if (empty($username) || empty($password)) {
        return ['success' => false, 'message' => '用户名和密码不能为空'];
    }
    $users = json_decode(file_get_contents(USER_FILE), true);
    foreach ($users as $user) {
        if ($user['username'] === $username) {
            if (password_verify($password, $user['password'])) {
                $token = bin2hex(random_bytes(32));
                $expiry = time() + 3600; // 1小时过期
                return [
                    'success' => true, 
                    'message' => '登录成功',
                    'token' => $token,
                    'expires' => $expiry,
                    'username' => $username
                ];
            } else {
                return ['success' => false, 'message' => '密码错误'];
            }
        }
    }
    
    return ['success' => false, 'message' => '用户不存在'];
}

// 输出JSON响应
echo json_encode(handleRequest());