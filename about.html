<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于我们</title>
    <link rel="stylesheet" href="css/common.css">
    <style>
        /* 关于我们页面特有样式 */
        .main-content {
            padding: 60px 0;
        }

        /* 英雄区域样式 */
        .hero {
            padding: 140px 0 60px;
            margin-top: 80px;
        }

        .hero h2 {
            font-size: 3.2rem;
            margin-bottom: 20px;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            max-width: 600px;
        }

        .hero-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        /* 关于我们容器 */
        .about-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            position: relative;
            z-index: 1;
        }

        /* 关于我们特定样式 */
        .about-section {
            margin-bottom: 60px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            padding: 60px 50px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .about-section:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .about-section h3 {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 40px;
            position: relative;
            padding-left: 30px;
            font-weight: 700;
            z-index: 1;
            letter-spacing: -0.01em;
        }

        .about-section h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 6px;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            border-radius: 3px;
            box-shadow: 0 0 15px rgba(255, 119, 48, 0.4);
        }

        .about-section h3::after {
            content: '';
            position: absolute;
            left: 30px;
            bottom: -8px;
            width: 80px;
            height: 3px;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            border-radius: 2px;
        }

        .about-section p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2rem;
            line-height: 1.8;
            margin-bottom: 25px;
            text-align: justify;
            text-indent: 2em;
            position: relative;
            z-index: 1;
            font-weight: 400;
        }

        .about-section p:first-of-type {
            font-size: 1.25rem;
            color: rgba(255, 255, 255, 0.95);
            font-weight: 500;
        }

        .about-section p:last-of-type {
            margin-bottom: 0;
        }



        /* 关于我们页面响应式设计 */
        @media (max-width: 768px) {
            .about-container {
                padding: 0 16px;
            }

            .hero {
                padding: 120px 0 50px;
            }

            .hero h2 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
                gap: 16px;
            }

            .about-section {
                padding: 40px 30px;
                margin-bottom: 40px;
            }

            .about-section h3 {
                font-size: 2rem;
                padding-left: 25px;
                margin-bottom: 30px;
            }

            .about-section p {
                font-size: 1.1rem;
                line-height: 1.7;
                text-indent: 1.5em;
            }
        }

        @media (max-width: 480px) {
            .hero h2 {
                font-size: 2rem;
            }

            .about-section {
                padding: 30px 20px;
                margin-bottom: 30px;
            }

            .about-section h3 {
                font-size: 1.8rem;
                padding-left: 20px;
                margin-bottom: 25px;
            }

            .about-section p {
                font-size: 1rem;
                line-height: 1.6;
                text-indent: 1em;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header" id="header">
        <div class="container nav-container">
            <div class="logo">
                <img src="img/logo.png" height="40" alt="Logo" />
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">游戏中心</a>
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html" class="active">关于我们</a>
            </div>
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns" style="display:none">
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 英雄区域 -->
    <div class="hero">
        <div class="container">
            <h2>关于我们</h2>
            <p>专业的游戏开发与运营团队，致力于为用户提供优质的娱乐体验</p>
            <!-- <div class="hero-buttons">
                <a href="products-app.html" class="hero-button primary-button">查看游戏</a>
                <a href="articles.html" class="hero-button secondary-button">防沉迷公告</a>
            </div> -->
        </div>
    </div>

    <div class="main-content">
        <div class="about-container">
            <!-- 公司简介 -->
            <div class="about-section">
                <h3>公司简介</h3>
                <p>广州立早网络科技有限公司成立于2016年，坐落于中国广州，是一家集自主研发、独代发行和产品运营等业务为一体的互联网游戏公司。</p>
                <p>自成立以来，立早网络保持着高速发展的势头，员工人数由最初的十余人发展至现在的近百人，其中90%为专业的游戏技术和运营人员，均来自国内各大游戏及相关互联网公司，拥有着丰富的研发、运营、推广和管理的经验。</p>
                <p>现已成功自研自发多款单机游戏和网络游戏，并获得数百款网络游戏的发行运营权，为公司产品的制作开发及运营发行提供了强有力的保证。</p>
                <p>用创新的精神研发和运营游戏是立早网络发展的重要目标，致力于为用户提供优质内容服务和良好的娱乐体验是立早网络发展的清晰使命。</p>
                <p>未来，立早网络将进一步提升核心竞争力，持续打造优质且经典的游戏作品，致力于将中国的游戏运作为世界的游戏。</p>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>
    <script src="js/login.js"></script>
    <script src="js/common.js"></script>
</body>
</html> 