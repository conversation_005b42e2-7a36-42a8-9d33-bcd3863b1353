/* 公用JavaScript - 现代科技风格交互效果 */

// 滚动效果
window.addEventListener('scroll', function() {
    const header = document.getElementById('header');
    if (header) {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    }
});

// 页面加载动画
document.addEventListener('DOMContentLoaded', function() {
    // 添加淡入动画
    const animateElements = document.querySelectorAll('.feature-card, .advantage-card, .stat-item, .game-card, .filter-section');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1
    });

    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // 英雄区域动画
    setTimeout(() => {
        const heroH2 = document.querySelector('.hero h2');
        if (heroH2) {
            heroH2.style.opacity = '1';
            heroH2.style.transform = 'translateY(0)';
        }
    }, 300);

    setTimeout(() => {
        const heroP = document.querySelector('.hero p');
        if (heroP) {
            heroP.style.opacity = '1';
            heroP.style.transform = 'translateY(0)';
        }
    }, 600);

    setTimeout(() => {
        const heroButtons = document.querySelector('.hero-buttons');
        if (heroButtons) {
            heroButtons.style.opacity = '1';
            heroButtons.style.transform = 'translateY(0)';
        }
    }, 900);
});

// 初始化英雄区域元素样式
document.addEventListener('DOMContentLoaded', function() {
    const heroElements = ['.hero h2', '.hero p', '.hero-buttons'];
    heroElements.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';
            element.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        }
    });
});

// 平滑滚动效果
function smoothScrollTo(target) {
    const element = document.querySelector(target);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 添加页面加载完成后的额外动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 为所有按钮添加点击波纹效果
    const buttons = document.querySelectorAll('.hero-button, .primary-button, .secondary-button, .publish-btn, .login-btn, .register-btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// 添加波纹效果的CSS样式
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .hero-button, .primary-button, .secondary-button, .publish-btn, .login-btn, .register-btn {
        position: relative;
        overflow: hidden;
    }
`;
document.head.appendChild(rippleStyle);

// 导航栏活跃状态管理
function updateActiveNavLink() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-links a');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        const linkHref = link.getAttribute('href');
        if (linkHref === currentPage || (currentPage === '' && linkHref === 'index.html')) {
            link.classList.add('active');
        }
    });
}

// 页面加载时更新导航栏状态
document.addEventListener('DOMContentLoaded', updateActiveNavLink);

// 添加键盘导航支持
document.addEventListener('keydown', function(e) {
    // ESC键关闭模态框或返回上一页
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal, .overlay');
        if (modals.length > 0) {
            modals.forEach(modal => {
                if (modal.style.display !== 'none') {
                    modal.style.display = 'none';
                }
            });
        }
    }
});

// 性能优化：防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 优化滚动事件性能
const optimizedScrollHandler = debounce(function() {
    const header = document.getElementById('header');
    if (header) {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    }
}, 10);

// 替换原有的滚动事件监听器
window.removeEventListener('scroll', window.scrollHandler);
window.addEventListener('scroll', optimizedScrollHandler);

// 添加页面可见性API支持
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时暂停动画
        document.body.style.animationPlayState = 'paused';
    } else {
        // 页面显示时恢复动画
        document.body.style.animationPlayState = 'running';
    }
});

// 导出常用函数供其他脚本使用
window.CommonUtils = {
    smoothScrollTo,
    debounce,
    updateActiveNavLink
};
