<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理软件开发</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
        }
        .header {
            background-color: #2196F3;
            padding: 20px;
            color: white;
        }
        .nav {
            background-color: #333;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .nav-container {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 50px;
        }
        .nav-links {
            display: flex;
            align-items: center;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 30px;
            padding: 5px 0;
            position: relative;
        }
        .nav-links a:hover::after,
        .nav-links a.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #2196F3;
        }
        .nav-links a.active {
            color: #2196F3;
        }
        .nav-auth {
            margin-right: auto;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .nav-auth a {
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
            margin-left: 15px;
        }
        .nav-auth a:first-child {
            margin-left: 0;
        }
        .nav-auth a::after {
            display: none;
        }
        .nav-auth .login-btn {
            color: #2196F3;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .nav-auth .login-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .nav-auth .register-btn {
            background-color: #2196F3;
            color: white;
        }
        .nav-auth .register-btn:hover {
            background-color: #1976D2;
        }
        .nav-auth i {
            margin-right: 4px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .products {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 40px;
        }
        .product-card {
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 0;
            transition: transform 0.3s, box-shadow 0.3s;
            background: white;
            overflow: hidden;
        }
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .product-card img {
            width: 100%;
            height: 200px;
            object-fit: fill;
        }
        .product-content {
            padding: 20px;
        }
        .product-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.5em;
        }
        .product-card p {
            color: #666;
            margin-bottom: 20px;
            min-height: 48px;
        }
        .product-features {
            list-style: none;
            margin: 15px 0;
        }
        .product-features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            color: #555;
        }
        .product-features li:before {
            content: '✓';
            color: #2196F3;
            position: absolute;
            left: 0;
        }
        .product-card a {
            display: inline-block;
            background-color: #2196F3;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 15px;
            transition: background-color 0.3s;
        }
        .product-card a:hover {
            background-color: #1976D2;
        }
        .footer {
            background-color: #333;
            color: white;
            padding: 50px 0 20px;
            margin-top: 60px;
        }
        .footer-content {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-bottom: 30px;
        }
        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 25px;
            color: #2196F3;
            position: relative;
            padding-bottom: 10px;
        }
        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background-color: #2196F3;
        }
        .footer-section ul {
            list-style: none;
        }
        .footer-section ul li {
            margin-bottom: 12px;
        }
        .footer-section ul li a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s;
            opacity: 0.8;
        }
        .footer-section ul li a:hover {
            color: #2196F3;
            opacity: 1;
        }
        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            opacity: 0.8;
            transition: opacity 0.3s;
        }
        .contact-info:hover {
            opacity: 1;
        }
        .contact-info i {
            margin-right: 12px;
            color: #2196F3;
            font-size: 1.1em;
        }
        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
        }
        .hero {
            text-align: center;
            padding: 50px 0;
            background-color: #f9f9f9;
            margin-bottom: 40px;
        }
        .hero h2 {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 20px;
        }
        .hero p {
            color: #666;
            font-size: 1.2em;
            max-width: 800px;
            margin: 0 auto;
        }
        .detail-button {
            display: inline-block;
            background-color: #2196F3;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 15px;
            transition: background-color 0.3s;
        }
        .detail-button:hover {
            background-color: #1976D2;
        }
        /* 发布按钮样式 */
        .publish-btn {
            background-color: #2196F3;
            color: white !important;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        
        .publish-btn:hover {
            background-color: #1976D2;
            transform: translateY(-2px);
        }
        
        .logout-btn {
            color: #fff !important;
            background-color: rgba(255, 255, 255, 0.1);
            margin-left: 15px;
            display: flex;
            align-items: center;
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .user-btns {
            display: flex;
            align-items: center;
        }
        
        .user-btns a {
            text-decoration: none;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .user-btns i {
            margin-right: 6px;
            font-size: 14px;
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="100" /></h1>
        </div>
    </div>
    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>我要发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>我要发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">APP开发</a>
                <a href="products-wx.html">小程序开发</a>
                <a href="products-soft.html" class="active">管理软件开发</a>
                <!-- <a href="solutions.html">解决方案</a> -->
                <!-- <a href="cases.html">成功案例</a> -->
                <a href="about.html">关于我们</a>
                <a href="contact.html">联系我们</a>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="products" id="productContainer">
            <!-- 产品卡片将通过JavaScript动态生成 -->
        </div>
    </div>
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">APP开发</a></li>
                        <li><a href="products-wx.html">小程序开发</a></li>
                        <li><a href="products-soft.html">管理软件开发</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                        <li><a href="contact.html">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 GuangZhouLiZao. All rights reserved.</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>
    <script src="js/login.js"></script>
    <script>
        // 管理软件产品数据
        const softwareProducts = [
            {
                id: 's1',
                name: 'OA办公系统',
                image: 'img/soft/1/主图.png',
                images: [
                    'img/soft/1/1.jpg',
                    'img/soft/1/2.jpg',
                    'img/soft/1/3.jpg',
                    'img/soft/1/4.jpg',
                    'img/soft/1/5.jpg',
                    'img/soft/1/6.jpg',
                ],
                description: '提升办公效率，优化管理流程，赋能企业数字化转型',
                features: [
                    '数据同步查看',
                    '可视化图标展示',
                    '高度自定义设置',
                    '多端口多设备',
                ],
                publisher: '李华',
                supplier: '',
                contactPhone: '135****5478'
            },
            {
                id: 's2',
                name: '企业管理系统',
                image: 'img/soft/2/主图.png',
                images: [
                    'img/soft/2/1.png',
                    'img/soft/2/2.png',
                    'img/soft/2/3.png',
                    'img/soft/2/4.png',
                ],
                description: '整合资源流程，提升运营效能，驱动业务增长',
                features: [
                    '打卡管理',
                    '请假管理',
                    '审批管理',
                    '项目管理',
                ],
                publisher: '陈思素',
                supplier: '',
                contactPhone: '157****5354'
            },
            {
                id: 's3',
                name: '食堂出入库管理系统',
                image: 'img/soft/3/主图.jpg',
                images: [
                    'img/soft/3/1.jpg',
                    'img/soft/3/2-2.jpg',
                    'img/soft/3/2.jpg',
                    'img/soft/3/3.jpg',
                    'img/soft/3/4.jpg',
                    'img/soft/3/5.jpg',
                    'img/soft/3/6.jpg',
                    'img/soft/3/7.jpg',
                    'img/soft/3/8.jpg',
                ],
                description: '精准管控食材，优化库存流转，降低运营成本',
                features: [
                    '出入库明细',
                    '保质期预警',
                    '食材出库盘点报废',
                    '报价单明细',
                ],
                publisher: '金丝科技',
                supplier: '',
                contactPhone: '135****8585'
            },
            {
                id: 's4',
                name: '计算机项目设计',
                image: 'img/soft/4/主图.jpg',
                images: [
                    'img/soft/4/1.png',
                    'img/soft/2/2.png',
                ],
                description: '实现技术创新，解决业务痛点，创造商业价值',
                features: [
                    '管理系统',
                    '商城系统',
                    'WEB网站',
                    '桌面软件',
                ],
                publisher: '天诺科技',
                supplier: '',
                contactPhone: '137****1254'
            },
            {
                id: 's5',
                name: 'NTF数藏平台开发',
                image: 'img/soft/5/主图.jpg',
                images: [
                    'img/soft/5/1.jpg',
                    'img/soft/5/2.jpg',
                    'img/soft/5/3.jpg',
                    'img/soft/5/4.jpg',
                    'img/soft/5/5.jpg',
                    'img/soft/5/6.jpg',
                    'img/soft/5/7.jpg',
                    'img/soft/5/8.jpg',
                    'img/soft/5/9.jpg',
                ],
                description: '确权数字资产，赋能艺术创作，重塑收藏生态',
                features: [
                    '数藏发售',
                    '数藏预售',
                    '数藏合成',
                    '多种合约',
                ],
                publisher: '安创网络',
                supplier: '',
                contactPhone: '138****4200'
            },
            {
                id: 's6',
                name: 'OA系统定制',
                image: 'img/soft/6/主图.png',
                images: [
                    'img/soft/6/1.png',
                    'img/soft/6/2.png',
                    'img/soft/6/3.png',
                    'img/soft/6/4.png',
                    'img/soft/6/5.png',
                    'img/soft/6/6.png',
                    'img/soft/6/7.png',
                    'img/soft/6/8.png',
                    'img/soft/6/9.png',
                ],
                description: '优化客户关系，提升销售转化，驱动业绩增长',
                features: [
                    '协同办公平台',
                    '可视化后台',
                    '进销存管理',
                    '客户关系管理',
                ],
                publisher: '连客云',
                supplier: '',
                contactPhone: '139****0020'
            },
            {
                id: 's7',
                name: 'ERP进存销系统',
                image: 'img/soft/7/主图.jpg',
                images: [
                    'img/soft/7/1.jpg',
                    'img/soft/7/2.jpg',
                    'img/soft/7/3.jpg',
                    'img/soft/7/4.jpg',
                    'img/soft/7/5.jpg',
                    'img/soft/7/6.jpg',
                    'img/soft/7/7.jpg'
                ],
                description: '整合进货、库存与销售流程，提升企业运营效率，降低管理成本',
                features: [
                    '进货管理',
                    '库存监控',
                    '销售分析',
                    '多维度报表'
                ],
                publisher: '捷径科技',
                supplier: '',
                contactPhone: '137****4511'
            },
            {
                id: 's8',
                name: '工业控制管理系统',
                image: 'img/soft/8/主图.png',
                images: [
                    'img/soft/8/1.jpg',
                ],
                description: '实时监控生产流程，优化工业自动化，提升生产效率与安全性',
                features: [
                    '实时数据监控',
                    '生产调度管理',
                    '设备预警维护',
                    '工艺流程控制'
                ],
                publisher: '唐图网络',
                supplier: '',
                contactPhone: '138****0539'
            },
            {
                id: 's9',
                name: '艺人管理系统',
                image: 'img/soft/9/主图.png',
                images: [
                    'img/soft/9/1.jpg',
                    'img/soft/9/2.jpg',
                    'img/soft/9/3.jpg',
                    'img/soft/9/4.jpg',
                    'img/soft/9/5.jpg',
                    'img/soft/9/6.jpg',
                    'img/soft/9/7.jpg',
                    'img/soft/9/8.jpg',
                    'img/soft/9/9.jpg',
                ],
                description: '高效管理艺人资源，优化排期安排，提升经纪效益',
                features: [
                    '艺人档案管理',
                    '演出排期规划',
                    '合约收益统计',
                    '粉丝互动分析'
                ],
                publisher: '优软科技',
                supplier: '',
                contactPhone: '186****1166'
            },
            {
                id: 's10',
                name: 'APP定制开发',
                image: 'img/soft/10/主图.png',
                images: [
                    'img/soft/10/1.jpg',
                ],
                description: '专业移动应用开发，打造专属品牌体验，拓展数字营销渠道',
                features: [
                    'UI/UX设计',
                    '功能定制开发',
                    '多平台适配',
                    '数据分析集成'
                ],
                publisher: '拓冠科技',
                supplier: '',
                contactPhone: '138****1720'
            },
            {
                id: 's11',
                name: 'CDK智能化管理',
                image: 'img/soft/11/主图.png',
                images: [
                    'img/soft/11/2.png',
                    'img/soft/11/3.png',
                    'img/soft/11/4.png',
                    'img/soft/11/5.png',
                    'img/soft/11/6.png',
                    'img/soft/11/7.png',
                    'img/soft/11/8.png',
                    'img/soft/11/9.png',
                ],
                description: '数字密钥智能管理，安全高效分发，提升授权体验',
                features: [
                    '批量密钥生成',
                    '安全授权分发',
                    '激活数据分析',
                    '防盗版保护'
                ],
                publisher: '木风科技',
                supplier: '',
                contactPhone: '159****1449'
            },
            {
                id: 's12',
                name: '万家软件',
                image: 'img/soft/12/主图.jpg',
                images: [
                    'img/soft/12/1.jpg',
                ],
                description: '优化密钥管理流程，简化授权操作，提升安全等级',
                features: [
                    '多级权限管理',
                    '实时监控统计',
                    '自动化授权',
                    '密钥生命周期管理'
                ],
                publisher: '万家软件',
                supplier: '',
                contactPhone: '136****3159'
            },
            {
                id: 's13',
                name: '可视化能源管理系统',
                image: 'img/soft/13/主图.jpg',
                images: [
                    'img/soft/13/1.jpg',
                    'img/soft/13/2.jpg',
                ],
                description: '直观监控能源使用，识别优化空间，降低能源成本',
                features: [
                    '能耗实时监测',
                    '用能趋势分析',
                    '节能方案推荐',
                    '碳排放计算'
                ],
                publisher: '林鑫心',
                supplier: '',
                contactPhone: '188****5037'
            }

        ];

        // 动态生成产品卡片
        function renderProducts() {
            const productContainer = document.getElementById('productContainer');
            
            softwareProducts.forEach(product => {
                // 创建产品卡片
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                
                // 创建产品图片
                const productImg = document.createElement('img');
                productImg.src = product.image;
                productImg.alt = product.name;
                
                // 创建产品内容容器
                const productContent = document.createElement('div');
                productContent.className = 'product-content';
                
                // 创建产品标题
                const productTitle = document.createElement('h3');
                productTitle.textContent = product.name;
                
                // 创建产品描述
                const productDesc = document.createElement('p');
                productDesc.textContent = product.description;
                
                // 创建产品特性列表
                const featuresList = document.createElement('ul');
                featuresList.className = 'product-features';
                
                // 添加特性项
                product.features.forEach(feature => {
                    const featureItem = document.createElement('li');
                    featureItem.textContent = feature;
                    featuresList.appendChild(featureItem);
                });
                
                // 创建详情按钮
                const detailButton = document.createElement('a');
                detailButton.href = `product-detail.html?id=${product.id}`;
                detailButton.className = 'detail-button';
                detailButton.textContent = '了解详情';
                
                // 组装产品卡片
                productContent.appendChild(productTitle);
                productContent.appendChild(productDesc);
                productContent.appendChild(featuresList);
                productContent.appendChild(detailButton);
                
                productCard.appendChild(productImg);
                productCard.appendChild(productContent);
                
                // 将产品卡片添加到容器
                productContainer.appendChild(productCard);
            });
        }

        // 页面加载时渲染产品
        document.addEventListener('DOMContentLoaded', renderProducts);
    </script>
</body>
</html> 