<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防沉迷公告</title>
    <link rel="stylesheet" href="css/common.css">
    <style>
        /* 文章页面特有样式 */
        .hero {
            padding: 140px 0 60px;
            margin-top: 80px;
        }

        .hero h2 {
            font-size: 3.2rem;
            margin-bottom: 20px;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            max-width: 600px;
        }

        .main-content {
            margin: 40px 0 0 0;
            padding: 60px 0;
        }

        /* 文章列表样式 */
        .articles-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 24px;
            position: relative;
            z-index: 1;
        }

        /* 分类标题样式 */
        .category-title {
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            color: white;
            padding: 16px 24px;
            margin: 40px 0 30px 0;
            border-radius: 12px;
            font-size: 1.25rem;
            font-weight: 700;
            position: relative;
            box-shadow: 0 8px 32px rgba(255, 119, 48, 0.3);
            border: 1px solid rgba(255, 119, 48, 0.2);
            letter-spacing: -0.01em;
        }

        /* 文章列表项样式 */
        .article-list {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
            margin-bottom: 32px;
        }

        .article-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24px 32px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .article-item:last-child {
            border-bottom: none;
        }

        .article-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateX(4px);
            border-left: 3px solid #ff7730;
        }

        .article-title {
            color: white;
            font-size: 1.125rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            letter-spacing: -0.01em;
        }

        .article-title:hover {
            color: #ff7730;
        }

        .article-date {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.9rem;
            min-width: 100px;
            text-align: right;
            font-weight: 500;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            margin: 60px 0;
        }

        .pagination a,
        .pagination span {
            padding: 12px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        .pagination a {
            color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .pagination a:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
        }

        .pagination .current {
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            color: white;
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.3);
            border: 1px solid rgba(255, 119, 48, 0.3);
        }

        .pagination .disabled {
            color: rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: not-allowed;
        }

        /* 文章页面响应式设计 */
        @media (max-width: 768px) {
            .hero {
                padding: 120px 0 50px;
            }

            .hero h2 {
                font-size: 2.5rem;
            }

            .hero p {
                font-size: 1rem;
            }

            .articles-container {
                padding: 0 16px;
            }

            .article-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
                padding: 20px 24px;
            }

            .article-date {
                text-align: left;
                min-width: auto;
            }

            .category-title {
                font-size: 1.125rem;
                padding: 14px 20px;
            }
        }

        @media (max-width: 480px) {
            .hero h2 {
                font-size: 2rem;
            }

            .category-title {
                font-size: 1rem;
                padding: 12px 16px;
            }

            .article-item {
                padding: 16px 20px;
            }

            .pagination {
                flex-wrap: wrap;
                gap: 8px;
            }

            .pagination a,
            .pagination span {
                padding: 10px 12px;
                font-size: 0.8rem;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header" id="header">
        <div class="container nav-container">
            <div class="logo">
                <img src="img/logo.png" height="40" alt="Logo" />
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">游戏中心</a>
                <a href="articles.html" class="active">防沉迷公告</a>
                <a href="about.html">关于我们</a>
            </div>
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns" style="display:none">
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 英雄区域 -->
    <div class="hero">
        <div class="container">
            <h2>📋 防沉迷公告</h2>
            <p>关注青少年健康游戏，共建绿色网络环境</p>
        </div>
    </div>

    <div class="main-content">
        <div class="articles-container">
            <!-- 防沉迷公告分类 -->
            <div class="category-title">防沉迷公告</div>
            <div class="article-list">
                <div class="article-item" data-article-id="jiachang-jianhu">
                    <a href="article-detail.html?id=jiachang-jianhu" class="article-title">家长监护工程介绍</a>
                    <span class="article-date">2025-01-15</span>
                </div>
                <div class="article-item" data-article-id="qingshaonian-fangchenmi">
                    <a href="article-detail.html?id=qingshaonian-fangchenmi" class="article-title">青少年防沉迷</a>
                    <span class="article-date">2025-01-10</span>
                </div>

            </div>


            <!-- 分页 -->
            <div class="pagination">
                <a href="#" class="disabled">上一页</a>
                <span class="current">1</span>
                <a href="#">下一页</a>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <script src="js/login.js"></script>
    <script src="js/common.js"></script>
    <script>
        // 文章点击事件
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.article-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    // 如果点击的不是链接本身，则触发链接点击
                    if (e.target.tagName !== 'A') {
                        const link = this.querySelector('.article-title');
                        if (link) {
                            window.location.href = link.href;
                        }
                    }
                });

                // 添加鼠标悬停效果
                item.addEventListener('mouseenter', function() {
                    this.style.cursor = 'pointer';
                });
            });
        });
    </script>
</body>
</html>
