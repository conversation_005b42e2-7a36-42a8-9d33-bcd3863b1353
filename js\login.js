// 检查登录状态
function checkLoginStatus() {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    const authBtns = document.getElementById('authBtns');
    const userBtns = document.getElementById('userBtns');
    
    if (isLoggedIn === 'true') {
        authBtns.style.display = 'none';
        userBtns.style.display = 'flex';
    } else {
        authBtns.style.display = 'flex';
        userBtns.style.display = 'none';
    }
}

// 处理退出登录
function handleLogout() {
    localStorage.removeItem('isLoggedIn');
    checkLoginStatus();
    // alert('退出成功');
}

// 页面加载时检查登录状态
document.addEventListener('DOMContentLoaded', checkLoginStatus);
