<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序开发</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
        }
        .header {
            background-color: #2196F3;
            padding: 20px;
            color: white;
        }
        .nav {
            background-color: #333;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .nav-container {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 50px;
        }
        .nav-links {
            display: flex;
            align-items: center;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 30px;
            padding: 5px 0;
            position: relative;
        }
        .nav-links a:hover::after,
        .nav-links a.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #2196F3;
        }
        .nav-links a.active {
            color: #2196F3;
        }
        .nav-auth {
            margin-right: auto;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .nav-auth a {
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
            margin-left: 15px;
        }
        .nav-auth a:first-child {
            margin-left: 0;
        }
        .nav-auth a::after {
            display: none;
        }
        .nav-auth .login-btn {
            color: #2196F3;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .nav-auth .login-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .nav-auth .register-btn {
            background-color: #2196F3;
            color: white;
        }
        .nav-auth .register-btn:hover {
            background-color: #1976D2;
        }
        .nav-auth i {
            margin-right: 4px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .products {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 40px;
        }
        .product-card {
            border: 1px solid #eee;
            border-radius: 10px;
            padding: 0;
            transition: transform 0.3s, box-shadow 0.3s;
            background: white;
            overflow: hidden;
        }
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .product-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        .product-content {
            padding: 20px;
        }
        .product-card h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.5em;
        }
        .product-card p {
            color: #666;
            margin-bottom: 20px;
            min-height: 48px;
        }
        .product-features {
            list-style: none;
            margin: 15px 0;
        }
        .product-features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            color: #555;
        }
        .product-features li:before {
            content: '✓';
            color: #2196F3;
            position: absolute;
            left: 0;
        }
        .product-card a {
            display: inline-block;
            background-color: #2196F3;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 15px;
            transition: background-color 0.3s;
        }
        .product-card a:hover {
            background-color: #1976D2;
        }
        .footer {
            background-color: #333;
            color: white;
            padding: 50px 0 20px;
            margin-top: 60px;
        }
        .footer-content {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-bottom: 30px;
        }
        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 25px;
            color: #2196F3;
            position: relative;
            padding-bottom: 10px;
        }
        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background-color: #2196F3;
        }
        .footer-section ul {
            list-style: none;
        }
        .footer-section ul li {
            margin-bottom: 12px;
        }
        .footer-section ul li a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s;
            opacity: 0.8;
        }
        .footer-section ul li a:hover {
            color: #2196F3;
            opacity: 1;
        }
        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            opacity: 0.8;
            transition: opacity 0.3s;
        }
        .contact-info:hover {
            opacity: 1;
        }
        .contact-info i {
            margin-right: 12px;
            color: #2196F3;
            font-size: 1.1em;
        }
        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
        }
        .hero {
            text-align: center;
            padding: 50px 0;
            background-color: #f9f9f9;
            margin-bottom: 40px;
        }
        .hero h2 {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 20px;
        }
        .hero p {
            color: #666;
            font-size: 1.2em;
            max-width: 800px;
            margin: 0 auto;
        }
        .detail-button {
            display: inline-block;
            background-color: #2196F3;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 15px;
            transition: background-color 0.3s;
        }
        .detail-button:hover {
            background-color: #1976D2;
        }
        /* 发布按钮样式 */
        .publish-btn {
            background-color: #2196F3;
            color: white !important;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        
        .publish-btn:hover {
            background-color: #1976D2;
            transform: translateY(-2px);
        }
        
        .logout-btn {
            color: #fff !important;
            background-color: rgba(255, 255, 255, 0.1);
            margin-left: 15px;
            display: flex;
            align-items: center;
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .user-btns {
            display: flex;
            align-items: center;
        }
        
        .user-btns a {
            text-decoration: none;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .user-btns i {
            margin-right: 6px;
            font-size: 14px;
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="100" /></h1>
        </div>
    </div>
    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>我要发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>我要发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">APP开发</a>
                <a href="products-wx.html" class="active">小程序开发</a>
                <a href="products-soft.html">管理软件开发</a>
                <a href="about.html">关于我们</a>
                <a href="contact.html">联系我们</a>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="products" id="productContainer">
            <!-- 产品卡片将通过JavaScript动态生成 -->
        </div>
    </div>
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <!-- <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div> -->
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">APP开发</a></li>
                        <li><a href="products-wx.html">小程序开发</a></li>
                        <li><a href="products-soft.html">管理软件开发</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                        <li><a href="contact.html">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 GuangZhouLiZao. All rights reserved.</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>
    <script src="js/login.js"></script>
    <script>
        // 小程序产品数据
        const wxProducts = [
            {
                id: 'w1',
                name: '好网互联',
                image: './img/wx/1/主图.jpg',
                images: [
                    'img/wx/1/1.jpg',
                    'img/wx/1/2.jpg',
                    'img/wx/1/3.jpg',
                    'img/wx/1/4.jpg',
                    'img/wx/1/5.jpg',
                ],
                description: '"专业小程序定制开发，满足多样商业需求',
                features: ["一站式开发解决方案", "精准营销数据分析", "系统集成与API对接", "持续技术支持与维护"],
                publisher: '好网互联',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '134****2669'
            },
            {
                id: 'w2',
                name: '莫畏科技',
                image: './img/wx/2/主图.jpg',
                images: [
                    'img/wx/2/1.jpg',
                ],
                description: '创新技术驱动，打造智能应用体验',
                features: ["智能互动小程序开发", "数据可视化解决方案", "用户行为分析系统", "云端服务器稳定支持"],
                publisher: '莫畏科技',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '135****9734'
            },
            {
                id: 'w3',
                name: '新光互动',
                image: './img/wx/3/主图.jpg',
                images: [
                    'img/wx/3/1.jpg',
                ],
                description: '专注互动体验设计，提升用户黏性',
                features: ["沉浸式用户界面设计", "互动游戏化元素融合", "社交分享功能强化", "精细化用户旅程规划"],
                publisher: '新光互动',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '159****8442'
            },
            {
                id: 'w4',
                name: '运动小程序',
                image: './img/wx/4/主图.jpg',
                images: [
                    'img/wx/4/1.jpg',
                    'img/wx/4/2.jpg',
                    'img/wx/4/3.jpg',
                    'img/wx/4/4.jpg',
                    'img/wx/4/5.jpg',
                    'img/wx/4/6.jpg',
                    'img/wx/4/7.jpg',
                    'img/wx/4/8.jpg',
                ],
                description: '科学健身指导，提升运动效率',
                features: ['个性化运动计划定制', '实时记录运动数据', '健康数据分析报告', '社区互动激励打卡'],
                publisher: '西陵区码农科技工作室',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '134****2669'
            },
            {
                id: 'w5',
                name: '预约小程序',
                image: './img/wx/5/主图.png',
                images: [
                    'img/wx/5/1.png',
                    'img/wx/5/2.png',
                    'img/wx/5/3.png',
                    'img/wx/5/4.png',
                ],
                description: '简化预约流程，提升服务效率',
                features: ['在线预约一键完成', '实时查看预约状态', '智能提醒避免遗漏', '多场景灵活适配'],
                publisher: '广州有家信息技术有限公司',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '187****7095'
            },
            {
                id: 'w6',
                name: '公交小程序',
                image: './img/wx/6/主图.jpg',
                images: [
                    'img/wx/6/1.jpg',
                    'img/wx/6/2.jpg',
                    'img/wx/6/3.jpg',
                    'img/wx/6/4.jpg',
                    'img/wx/6/5.jpg',
                    'img/wx/6/6.jpg',
                ],
                description: '实时公交查询，出行更便捷',
                features: ['线路规划智能推荐', '到站时间精准预测', '换乘方案一目了然', '支持离线查询功能'],
                publisher: '郑州泛微信息技术有限公司',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '135****0507'
            },
            {
                id: 'w7',
                name: '刷题小程序',
                image: './img/wx/7/主图.jpg',
                images: [
                    'img/wx/7/1.jpg',
                    'img/wx/7/2.jpg',
                    'img/wx/7/3.jpg',
                    'img/wx/7/4.jpg',
                    'img/wx/7/5.jpg',
                ],
                description: '高效学习工具，助力知识巩固',
                features: ['海量题库分类清晰', '智能错题自动收录', '模拟考试真实体验', '学习进度实时追踪'],
                publisher: '河南星科万讯智能科技有限公司',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '135****0484'
            },
            {
                id: 'w8',
                name: '盲盒小程序',
                image: './img/wx/8/主图.jpg',
                images: [
                    'img/wx/8/1.jpg',
                    'img/wx/8/2.jpg',
                    'img/wx/8/3.jpg',
                    'img/wx/8/4.jpg',
                    'img/wx/8/5.jpg',
                ],
                description: '惊喜购物体验，乐趣无限延伸',
                features: ['多样主题盲盒选择', '限量款隐藏款惊喜', '在线开盒即时互动', '社区分享交流乐趣'],
                publisher: '浙江企蜂信息技术有限公司',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '159****4366'
            },
            {
                id: 'w9',
                name: '商城小程序',
                image: './img/wx/9/主图.jpg',
                images: [
                    'img/wx/9/1.png',
                    'img/wx/9/2.png',
                    'img/wx/9/3.png',
                    'img/wx/9/4.png',
                    'img/wx/9/5.png',
                    'img/wx/9/6.jpg',
                ],
                description: '一站式购物平台，便捷又实惠',
                features: ['商品分类清晰直观', '多种支付方式支持', '促销活动实时更新', '订单物流全程追踪'],
                publisher: '张经理',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '020-38383518'
            },
            {
                id: 'w10',
                name: '艺界都市',
                image: './img/wx/10/主图.jpg',
                images: [
                    'img/wx/10/2.gif',
                    'img/wx/10/3.gif',
                    'img/wx/10/4.gif',
                    'img/wx/10/5.gif',
                    'img/wx/10/6.gif',
                    'img/wx/10/7.gif',
                    'img/wx/10/8.gif',
                    'img/wx/10/9.gif',
                ],
                description: '艺术文化数字平台，连接创作与鉴赏',
                features: ["艺术作品在线展示交易", "艺术活动实时推送预约", "创作者社区交流互动", "艺术教育资源分享"],
                publisher: '重庆平飞亚科技有限公司',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '135****6397'
            },
            {
                id: 'w12',
                name: 'JiZhan机赞',
                image: './img/wx/12/主图.jpg',
                images: [
                    'img/wx/12/2.jpg',
                    'img/wx/12/3.jpg',
                    'img/wx/12/4.jpg',
                    'img/wx/12/6.jpg',
                    'img/wx/12/7.jpg',
                    'img/wx/12/8.jpg',
                    'img/wx/12/9.jpg',
                    'img/wx/12/10.jpg',
                ],
                description: '智能硬件互联平台，科技赋能生活',
                features: ["智能设备一键连接控制", "数据同步与健康管理", "设备故障智能诊断", "个性化使用场景推荐"],
                publisher: '广东机赞智能科技有限公司',
                supplier: '广州飞羽科技有限公司',
                contactPhone: '159****3222'
            }
        ];

        // 动态生成产品卡片
        function renderProducts() {
            const productContainer = document.getElementById('productContainer');
            
            wxProducts.forEach(product => {
                // 创建产品卡片
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                
                // 创建产品图片
                const productImg = document.createElement('img');
                productImg.src = product.image;
                productImg.alt = product.name;
                
                // 创建产品内容容器
                const productContent = document.createElement('div');
                productContent.className = 'product-content';
                
                // 创建产品标题
                const productTitle = document.createElement('h3');
                productTitle.textContent = product.name;
                
                // 创建产品描述
                const productDesc = document.createElement('p');
                productDesc.textContent = product.description;
                
                // 创建产品特性列表
                const featuresList = document.createElement('ul');
                featuresList.className = 'product-features';
                
                // 添加特性项
                product.features.forEach(feature => {
                    const featureItem = document.createElement('li');
                    featureItem.textContent = feature;
                    featuresList.appendChild(featureItem);
                });
                
                // 创建详情按钮
                const detailButton = document.createElement('a');
                detailButton.href = `product-detail.html?id=${product.id}`;
                detailButton.className = 'detail-button';
                detailButton.textContent = '了解详情';
                
                // 组装产品卡片
                productContent.appendChild(productTitle);
                productContent.appendChild(productDesc);
                productContent.appendChild(featuresList);
                productContent.appendChild(detailButton);
                
                productCard.appendChild(productImg);
                productCard.appendChild(productContent);
                
                // 将产品卡片添加到容器
                productContainer.appendChild(productCard);
            });
        }

        // 页面加载时渲染产品
        document.addEventListener('DOMContentLoaded', renderProducts);
    </script>
</body>
</html> 