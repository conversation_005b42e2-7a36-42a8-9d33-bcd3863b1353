<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布信息</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 复用样式 */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        /* 头部和导航 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px 0;
            color: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        /* 主要内容区域 */
        .main-content {
            padding: 60px 0;
        }

        .publish-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 50px 40px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .publish-title {
            text-align: center;
            margin-bottom: 40px;
            color: #2c3e50;
            font-size: 2.2em;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: #2c3e50;
            font-weight: 500;
            font-size: 1.05em;
        }

        .form-group input[type="text"],
        .form-group input[type="email"],
        .form-group input[type="tel"],
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-group textarea {
            height: 150px;
            resize: vertical;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .upload-btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            color: #2c3e50;
        }

        .upload-btn:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }

        .submit-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .agreement {
            margin: 25px 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .agreement input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .agreement label {
            color: #5a6c7d;
            font-size: 14px;
            font-weight: 500;
        }

        .agreement a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .agreement a:hover {
            color: #764ba2;
            text-decoration: underline;
        }

        /* 导航样式 */
        .nav-links {
            display: flex;
            align-items: center;
            gap: 40px;
        }

        .nav-links a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .nav-links a:hover,
        .nav-links a.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* 登录注册按钮样式 */
        .nav-auth {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-auth a {
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-auth .login-btn {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .nav-auth .login-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .nav-auth .register-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-auth .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* 发布按钮样式 */
        .publish-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
            color: white !important;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .publish-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .logout-btn {
            color: #2c3e50 !important;
            background: rgba(44, 62, 80, 0.1);
            border: 1px solid rgba(44, 62, 80, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .logout-btn:hover {
            background: rgba(44, 62, 80, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(44, 62, 80, 0.2);
        }

        .user-btns {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-btns a {
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* 页脚样式 */
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 40px 0 20px;
            margin-top: 0;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            /*margin-bottom: 50px;*/
            position: relative;
            z-index: 1;
        }

        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: white;
            position: relative;
            padding-bottom: 8px;
            font-weight: 600;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 8px;
        }

        .footer-section ul li a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.95em;
            display: inline-block;
        }

        .footer-section ul li a:hover {
            color: #667eea;
            transform: translateX(5px);
        }

        .contact-info {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .contact-info:hover {
            color: white;
            transform: translateX(5px);
        }

        .contact-info i {
            margin-right: 10px;
            color: #667eea;
            font-size: 1em;
            width: 16px;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
            position: relative;
            z-index: 1;
        }

        .footer-bottom p {
            margin-bottom: 5px;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .footer-bottom a {
            color: rgba(255,255,255,0.8);
            transition: color 0.3s ease;
        }

        .footer-bottom a:hover {
            color: #667eea;
        }

        /* 修改发布按钮样式 */
        .publish-btn {
            background-color: #2196F3;
            color: white !important;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
        }
        
        .publish-btn:hover {
            background-color: #1976D2;
            transform: translateY(-2px);
        }
        
        .logout-btn {
            color: #fff !important;
            background-color: rgba(255, 255, 255, 0.1);
            margin-left: 15px;
            display: flex;
            align-items: center;
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        
        .user-btns {
            display: flex;
            align-items: center;
        }
        
        .user-btns a {
            text-decoration: none;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .user-btns i {
            margin-right: 6px;
            font-size: 14px;
        }

        /* 添加文件预览样式 */
        .file-preview-container {
            margin-top: 15px;
        }

        .file-preview-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-top: 10px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .file-icon {
            color: #667eea;
            font-size: 20px;
        }

        .file-name {
            color: #2c3e50;
            font-weight: 500;
            font-size: 14px;
            word-break: break-all;
        }

        .remove-file {
            background: #ff4d4f;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 10px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .remove-file:hover {
            background: #ff7875;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 20px;
            }

            .publish-container {
                padding: 40px 25px;
            }

            .publish-title {
                font-size: 1.8em;
            }

            .nav-links {
                gap: 20px;
            }

            .nav-auth {
                gap: 15px;
            }

            .nav-auth a,
            .user-btns a {
                padding: 10px 20px;
                font-size: 0.9em;
            }
        }

        @media (max-width: 480px) {
            .publish-title {
                font-size: 1.6em;
            }

            .nav-container {
                flex-direction: column;
                height: auto;
                gap: 20px;
            }

            .publish-container {
                padding: 30px 20px;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="50" /></h1>
        </div>
    </div>
    
    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">游戏中心</a>
                <!-- <a href="products-wx.html">小程序开发</a>
                <a href="products-soft.html">管理软件开发</a> -->
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
                <!-- <a href="contact.html">联系我们</a> -->
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="publish-container">
            <h2 class="publish-title">发布信息</h2>
            <form id="publishForm">
                <div class="form-group">
                    <label for="infoType">厂家名称 <span style="color: #ff6b6b;">*</span></label>
                    <input type="text" id="infoType" name="infoType" required>
                </div>

                <div class="form-group">
                    <label for="infoTitle">详细地址 <span style="color: #ff6b6b;">*</span></label>
                    <input type="text" id="infoTitle" name="infoTitle" required>
                </div>

                <!-- <div class="form-group">
                    <label for="companyName">公司名称 <span style="color: #ff6b6b;">*</span></label>
                    <input type="text" id="companyName" name="companyName" required>
                </div> -->


                <div class="form-group">
                    <label for="phone">联系电话 <span style="color: #ff6b6b;">*</span></label>
                    <input type="tel" id="phone" name="phone" required>
                </div>

                <div class="form-group">
                    <label for="infoDesc">游戏简介 <span style="color: #ff6b6b;">*</span></label>
                    <textarea id="infoDesc" name="infoDesc" required placeholder=""></textarea>
                </div>

                
                <!--
                <div class="form-group">
                    <label for="email">E-mail <span style="color: #ff6b6b;">*</span></label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="address">地址 <span style="color: #ff6b6b;">*</span></label>
                    <input type="text" id="address" name="address" required>
                </div>
                -->
                <div class="form-group">
                    <label for="infoImage">上传文件 <span style="color: #ff6b6b;">*</span></label>
                    <input type="file" id="infoImage" name="infoImage" style="display: none;">
                    <button type="button" class="upload-btn" onclick="document.getElementById('infoImage').click()">
                        <i class="fas fa-upload"></i> 选取文件
                    </button>
                    <div class="file-preview-container" id="filePreviewContainer"></div>
                </div>

                <button type="submit" class="submit-btn">
                    <i class="fas fa-paper-plane"></i> 提交发布
                </button>
            </form>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>
    <script src="js/login.js"></script>
    <script>
        if (localStorage.getItem('isLoggedIn') != 'true') {
            alert('请先登录')
            window.location.href = 'login.html'
        }

        // 处理文件预览
        document.getElementById('infoImage').addEventListener('change', function(e) {
            const container = document.getElementById('filePreviewContainer');
            const file = e.target.files[0];

            // 清除之前的文件显示
            container.innerHTML = '';

            if (file) {
                // 获取文件图标
                const getFileIcon = (fileName) => {
                    const extension = fileName.split('.').pop().toLowerCase();
                    const iconMap = {
                        'pdf': 'fas fa-file-pdf',
                        'doc': 'fas fa-file-word',
                        'docx': 'fas fa-file-word',
                        'xls': 'fas fa-file-excel',
                        'xlsx': 'fas fa-file-excel',
                        'ppt': 'fas fa-file-powerpoint',
                        'pptx': 'fas fa-file-powerpoint',
                        'txt': 'fas fa-file-alt',
                        'zip': 'fas fa-file-archive',
                        'rar': 'fas fa-file-archive',
                        'jpg': 'fas fa-file-image',
                        'jpeg': 'fas fa-file-image',
                        'png': 'fas fa-file-image',
                        'gif': 'fas fa-file-image',
                        'mp4': 'fas fa-file-video',
                        'avi': 'fas fa-file-video',
                        'mp3': 'fas fa-file-audio',
                        'wav': 'fas fa-file-audio'
                    };
                    return iconMap[extension] || 'fas fa-file';
                };

                const fileItem = document.createElement('div');
                fileItem.className = 'file-preview-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <i class="${getFileIcon(file.name)} file-icon"></i>
                        <span class="file-name">${file.name}</span>
                    </div>
                    <button type="button" class="remove-file" onclick="removeFile()">删除</button>
                `;
                container.appendChild(fileItem);
            }
        });

        // 删除文件函数
        function removeFile() {
            document.getElementById('infoImage').value = '';
            document.getElementById('filePreviewContainer').innerHTML = '';
        }

        // 修改表单提交处理
        document.getElementById('publishForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 检查是否上传了文件
            const fileInput = document.getElementById('infoImage');
            if (!fileInput.files || fileInput.files.length === 0) {
                alert('请上传一个文件！');
                return false;
            }

            // 检查其他必填字段
            // const requiredFields = ['infoType', 'infoTitle', 'infoDesc', 'companyName', 'contact', 'phone', 'email', 'address'];
            // for (let fieldId of requiredFields) {
            //     const field = document.getElementById(fieldId);
            //     if (!field.value.trim()) {
            //         alert(`请填写${field.previousElementSibling.textContent.replace(' *', '')}`);
            //         field.focus();
            //         return false;
            //     }
            // }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(document.getElementById('phone').value)) {
                alert('请输入正确的手机号码');
                document.getElementById('phone').focus();
                return false;
            }

            // 验证邮箱格式
            // const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            // if (!emailRegex.test(document.getElementById('email').value)) {
            //     alert('请输入正确的邮箱地址');
            //     document.getElementById('email').focus();
            //     return false;
            // }

            // console.log('文件数据:', fileInput.files[0]);

            var formData = new FormData(this);
            var body = new URLSearchParams(formData).toString();
            fetch('index.php?action=publish',{
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: body
            }).then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP 错误! 状态码: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('获取的数据:', data);
                    alert('信息已提交，请耐心等候后台审核');
                    location.reload();
                })
                .catch(error => {
                    console.error('获取数据时出错:', error);
                });
        });
    </script>
</body>
</html> 