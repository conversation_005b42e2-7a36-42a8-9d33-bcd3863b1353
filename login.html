<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录</title>
    <link rel="stylesheet" href="css/common.css">
    <style>
        /* 登录页面特有样式 */
        .main-content {
            margin: 40px 0 0 0;
            padding: 60px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: calc(100vh - 160px);
        }

        /* 登录表单样式 */
        .login-container {
            max-width: 450px;
            width: 100%;
            margin: 0 auto;
            padding: 50px 40px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 119, 48, 0.1) 0%, rgba(120, 119, 198, 0.1) 100%);
            opacity: 0.5;
            z-index: -1;
        }

        .login-title {
            text-align: center;
            margin-bottom: 40px;
            color: white;
            font-size: 2.2rem;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        .form-group input {
            width: 100%;
            padding: 16px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-group input:focus {
            outline: none;
            border-color: #ff7730;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(255, 119, 48, 0.2);
            transform: translateY(-2px);
        }

        .login-button {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #ff7730 0%, #7877c6 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(255, 119, 48, 0.3);
            letter-spacing: 0.5px;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(255, 119, 48, 0.4);
        }

        .login-links {
            margin-top: 30px;
            text-align: center;
        }

        .login-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 14px;
            letter-spacing: 0.5px;
        }

        .login-links a:hover {
            color: #ff7730;
            text-decoration: underline;
        }



        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 0 20px;
            }

            .login-container {
                padding: 40px 25px;
            }

            .login-title {
                font-size: 1.8rem;
            }

            .nav-links {
                gap: 16px;
            }

            .nav-auth {
                gap: 12px;
            }
        }

        @media (max-width: 480px) {
            .login-title {
                font-size: 1.6rem;
            }

            .login-container {
                padding: 30px 20px;
            }

            .nav-links {
                display: none;
            }
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header" id="header">
        <div class="container nav-container">
            <div class="logo">
                <img src="img/logo.png" height="50" />
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">游戏中心</a>
                <a href="articles.html">防沉迷公告</a>
                <a href="about.html">关于我们</a>
            </div>
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns" style="display:none">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>信息发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="login-container">
                <h2 class="login-title">用户登录</h2>
                <form id="loginForm">
                    <div class="form-group">
                        <label for="phone">手机号</label>
                        <input
                        type="tel"
                        id="phone"
                        name="phone"
                        required
                        pattern="^1[3-9]\d{9}$"
                        placeholder="请输入手机号"
                        oninvalid="this.setCustomValidity('请输入正确的11位手机号码')"
                        oninput="this.setCustomValidity('')">
                    </div>
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" required placeholder="请输入密码">
                    </div>
                    <button type="submit" class="login-button">登录</button>
                    <div class="login-links">
                        <a href="register.html">立即注册</a>
                        <!-- <a href="#">忘记密码？</a> -->
                    </div>
                </form>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <span>020-38383518</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>广州市天河区黄埔大道中660号之-2301、2302房</span>
                    </div>
                    <div class="contact-info">
                        <i class="fas fa-location"></i>
                        <span>广州立早网络科技有限公司</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="products-app.html">游戏中心</a></li>
                        <li><a href="articles.html">防沉迷公告</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。</p>
                <p><a href="https://beian.miit.gov.cn" style="color:white">粤ICP备17009698号</a></p>
            </div>
        </div>
    </footer>

    <script src="js/common.js"></script>
    <script>
    // 登录表单处理
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();

        var formData = new FormData(this);
        var body = new URLSearchParams(formData).toString();
        fetch('index.php?action=login',{
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: body
        }).then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP 错误! 状态码: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('获取的数据:', data);
                if (data.success) {
                    localStorage.setItem('isLoggedIn', 'true');
                    window.location.href = 'index.html';
                } else {
                    alert(data.message)
                }
            })
            .catch(error => {
                console.error('获取数据时出错:', error);
            });
    });
    </script>
</body>
</html> 