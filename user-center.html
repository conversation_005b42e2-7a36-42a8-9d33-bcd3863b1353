<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 复用现有头部和导航样式 */
        .header {
            background-color: #2196F3;
            padding: 20px;
            color: white;
        }

        /* 个人中心布局 */
        .user-center {
            display: flex;
            margin: 20px auto;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: #fff;
            border-right: 1px solid #eee;
            padding: 20px 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .menu-group {
            margin-bottom: 20px;
        }

        .menu-title {
            padding: 10px 20px;
            color: #666;
            font-size: 16px;
            font-weight: bold;
        }

        .menu-item {
            padding: 12px 20px 12px 40px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s;
        }

        .menu-item:hover {
            background: #f5f5f5;
            color: #2196F3;
        }

        .menu-item.active {
            background: #e3f2fd;
            color: #2196F3;
            border-right: 3px solid #2196F3;
        }

        /* 主内容区域样式 */
        .main-content {
            flex: 1;
            padding: 20px;
            min-height: 600px;
        }

        /* 订单列表样式 */
        .order-tabs {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }

        .tab-item {
            padding: 10px 20px;
            cursor: pointer;
            color: #666;
            position: relative;
        }

        .tab-item.active {
            color: #2196F3;
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background: #2196F3;
        }

        /* 搜索框样式 */
        .search-bar {
            display: flex;
            margin-bottom: 20px;
        }

        .search-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }

        .search-button {
            padding: 8px 20px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        /* 订单表格样式 */
        .order-table {
            width: 100%;
            border-collapse: collapse;
        }

        .order-table th,
        .order-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .order-table th {
            background: #f5f5f5;
            font-weight: normal;
            color: #666;
        }

        .empty-tip {
            text-align: center;
            padding: 40px;
            color: #999;
        }

        .empty-tip a {
            color: #2196F3;
            text-decoration: none;
        }

        /* 添加评价相关样式 */
        .review-content {
            padding: 20px 0;
        }
        
        .review-tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 20px;
        }
        
        .review-tabs .tab-item {
            padding: 10px 20px;
            font-size: 14px;
            color: #333;
            cursor: pointer;
            position: relative;
            margin-right: 20px;
        }
        
        .review-tabs .tab-item.active {
            color: #2196F3;
        }
        
        .review-tabs .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: #2196F3;
        }
        
        .empty-state {
            text-align: center;
            padding: 100px 0;
            color: #999;
        }
        
        .empty-state a {
            color: #2196F3;
            text-decoration: none;
        }
        
        .empty-state a:hover {
            text-decoration: underline;
        }

        /* 退货记录页面样式 */
        .returns-content {
            padding: 20px;
        }

        .returns-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .returns-header h2 {
            font-size: 18px;
            font-weight: normal;
            color: #333;
        }

        .returns-tabs {
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }

        .returns-table {
            width: 100%;
            border-collapse: collapse;
        }

        .returns-table th,
        .returns-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }

        .returns-table th {
            background: #f5f5f5;
            color: #666;
            font-weight: normal;
        }

        .search-bar {
            display: flex;
            gap: 10px;
        }

        .search-input {
            width: 200px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .search-button {
            padding: 8px 20px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .search-button:hover {
            background: #1976D2;
        }

        .empty-tip {
            text-align: center;
            padding: 40px;
            color: #999;
        }

        .empty-tip a {
            color: #2196F3;
            text-decoration: none;
        }

        .empty-tip a:hover {
            text-decoration: underline;
        }

        /* 消息通知页面样式 */
        .messages-content {
            padding: 20px;
        }

        .messages-tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 30px;
        }

        .messages-tabs .tab-item {
            padding: 10px 30px;
            font-size: 14px;
            color: #333;
            cursor: pointer;
            position: relative;
            margin-right: 20px;
        }

        .messages-tabs .tab-item.active {
            color: #2196F3;
        }

        .messages-tabs .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: #2196F3;
        }

        .empty-message {
            text-align: center;
            padding: 100px 0;
        }

        .empty-message .empty-tip {
            color: #999;
        }

        .empty-message .empty-tip i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #ccc;
        }

        .empty-message .empty-tip p {
            font-size: 14px;
        }

        /* 消息列表样式 - 当有消息时使用 */
        .message-list {
            display: none; /* 默认隐藏，有消息时显示 */
        }

        .message-item {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: flex-start;
        }

        .message-icon {
            margin-right: 15px;
            color: #2196F3;
        }

        .message-content {
            flex: 1;
        }

        .message-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
        }

        .message-time {
            font-size: 12px;
            color: #999;
        }

        .message-desc {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            margin-top: 8px;
        }

        /* 在现有样式后添加导航和页脚样式 */
        .nav-auth {
            margin-right: auto;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .nav-auth a {
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
            margin-left: 15px;
        }

        .nav-auth a:first-child {
            margin-left: 0;
        }

        .nav-auth a::after {
            display: none;
        }

        .nav-auth .login-btn {
            color: #2196F3;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .nav-auth .login-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .nav-auth .register-btn {
            background-color: #2196F3;
            color: white;
        }

        .nav-auth .register-btn:hover {
            background-color: #1976D2;
        }

        .nav-auth i {
            margin-right: 4px;
        }

        /* 页脚样式 */
        .footer {
            background-color: #333;
            color: white;
            padding: 50px 0 20px;
            margin-top: 60px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-bottom: 30px;
        }

        .footer-section h4 {
            font-size: 1.2em;
            margin-bottom: 25px;
            color: #2196F3;
            position: relative;
            padding-bottom: 10px;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 30px;
            height: 2px;
            background-color: #2196F3;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section ul li {
            margin-bottom: 12px;
        }

        .footer-section ul li a {
            color: #fff;
            text-decoration: none;
            transition: color 0.3s;
            opacity: 0.8;
        }

        .footer-section ul li a:hover {
            opacity: 1;
            color: #2196F3;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: rgba(255,255,255,0.6);
        }

        /* 用户按钮样式 */
        .user-btns {
            display: flex;
            align-items: center;
        }

        .user-btns a {
            text-decoration: none;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .user-btns i {
            margin-right: 6px;
            font-size: 14px;
        }

        .publish-btn {
            background-color: #2196F3;
            color: white !important;
        }

        .publish-btn:hover {
            background-color: #1976D2;
            transform: translateY(-2px);
        }

        .logout-btn {
            color: #fff !important;
            background-color: rgba(255, 255, 255, 0.1);
            margin-left: 15px;
        }

        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* 在现有样式中添加/更新以下导航样式 */
        .nav {
            background-color: #333;
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            display: flex;
            justify-content: flex-end; /* 改为右对齐 */
            align-items: center;
            height: 50px; /* 固定高度 */
        }

        .nav-links {
            display: flex;
            align-items: center;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 30px;
            padding: 5px 0;
            position: relative;
        }

        .nav-links a:hover::after,
        .nav-links a.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #2196F3;
        }

        .nav-links a.active {
            color: #2196F3;
        }

        /* 登录/注册按钮样式 */
        .auth-btns {
            display: flex;
            align-items: center;
        }

        .auth-btns a {
            text-decoration: none;
            padding: 6px 15px;
            border-radius: 4px;
            transition: all 0.3s;
            margin-left: 15px;
            color: white;
        }

        .register-btn {
            background-color: #2196F3;
        }

        .register-btn:hover {
            background-color: #1976D2;
        }

        .login-btn {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .login-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
    </style>
    <link rel="stylesheet" href="css/all.min.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1><img src="img/logo.png" height="100" /></h1>
        </div>
    </div>

    <div class="nav">
        <div class="container nav-container">
            <div class="nav-auth" id="navAuth">
                <!-- 登录前显示 -->
                <div class="auth-btns" id="authBtns">
                    <a href="register.html" class="register-btn">
                        <i class="fas fa-user-plus"></i>注册
                    </a>
                    <a href="login.html" class="login-btn">
                        <i class="fas fa-user"></i>登录
                    </a>
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>我要发布
                    </a>
                </div>
                <!-- 登录后显示 -->
                <div class="user-btns" id="userBtns" style="display: none;">
                    <a href="publish.html" class="publish-btn">
                        <i class="fas fa-edit"></i>我要发布
                    </a>
                    <a href="javascript:void(0)" class="logout-btn" onclick="handleLogout()">
                        <i class="fas fa-sign-out-alt"></i>退出
                    </a>
                </div>
            </div>
            <div class="nav-links">
                <a href="index.html">首页</a>
                <a href="products-app.html">APP开发</a>
                <a href="products-wx.html">小程序开发</a>
                <a href="products-soft.html">管理软件开发</a>
                <a href="about.html">关于我们</a>
                <a href="contact.html">联系我们</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="user-center">
            <div class="sidebar">
                <ul class="sidebar-menu">
                    <li class="menu-group">
                        <div class="menu-title">交易中心</div>
                        <div class="menu-item active">我的订单</div>
                        <div class="menu-item">虚拟订单</div>
                        <div class="menu-item">我的评价</div>
                    </li>
                    <!-- <li class="menu-group">
                        <div class="menu-title">资产中心</div>
                        <div class="menu-item">我的优惠券</div>
                        <div class="menu-item">账户余额</div>
                    </li>
                    <li class="menu-group">
                        <div class="menu-title">关注中心</div>
                        <div class="menu-item">我的收藏</div>
                        <div class="menu-item">我的足迹</div>
                    </li>
                    <li class="menu-group">
                        <div class="menu-title">个人中心</div>
                        <div class="menu-item">个人信息</div>
                        <div class="menu-item">地址管理</div>
                        <div class="menu-item">安全设置</div>
                    </li> -->
                    <li class="menu-group">
                        <div class="menu-title">服务中心</div>
                        <div class="menu-item">退货管理</div>
                        <div class="menu-item">消息通知</div>
                    </li>
                </ul>
            </div>
            <div class="main-content" id="mainContent">
                <div class="order-tabs">
                    <div class="tab-item active">全部订单</div>
                    <div class="tab-item">待付款</div>
                    <div class="tab-item">待发货</div>
                    <div class="tab-item">待收货</div>
                    <div class="tab-item">待评论</div>
                </div>
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="商品名称、订单编号">
                    <button class="search-button">查询</button>
                </div>
                <table class="order-table">
                    <thead>
                        <tr>
                            <th>商品信息</th>
                            <th>单价</th>
                            <th>数量</th>
                            <th>订单总金额</th>
                            <th>订单状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="5">
                                <div class="empty-tip">
                                    您还没有订单，快去<a href="index.html">逛逛</a>吧~
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>关于我们</h4>
                    <ul>
                        <li><a href="about.html">公司简介</a></li>
                        <li><a href="about.html#team">团队介绍</a></li>
                        <li><a href="about.html#culture">企业文化</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>产品服务</h4>
                    <ul>
                        <li><a href="products-app.html">APP开发</a></li>
                        <li><a href="products-wx.html">小程序开发</a></li>
                        <li><a href="products-soft.html">管理软件开发</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>了解我们</h4>
                    <ul>
                        <li><a href="about.html">关于我们</a></li>
                        <li><a href="contact.html">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 软件开发服务平台. All rights reserved.</p>
            </div>
        </div>
    </div>

    <script>
        // 检查是否登录
        function checkAuth() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn !== 'true') {
                // 未登录，跳转到登录页面，并带上当前页面URL作为参数
                window.location.href = 'login.html';
            }
        }

        // 页面加载完成后初始化事件
        document.addEventListener('DOMContentLoaded', () => {
            // 检查登录状态
            checkAuth();
            
            // 定义各个页面的内容模板
            const pageTemplates = {
                // 我的订单页面模板
                'orders': `
                    <div class="order-tabs">
                        <div class="tab-item active">全部订单</div>
                        <div class="tab-item">待付款</div>
                        <div class="tab-item">待发货</div>
                        <div class="tab-item">待收货</div>
                        <div class="tab-item">待评论</div>
                    </div>
                    <div class="search-bar">
                        <input type="text" class="search-input" placeholder="商品名称、订单编号">
                        <button class="search-button">查询</button>
                    </div>
                    <table class="order-table">
                        <thead>
                            <tr>
                                <th>商品信息</th>
                                <th>单价</th>
                                <th>数量</th>
                                <th>订单总金额</th>
                                <th>订单状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5">
                                    <div class="empty-tip">
                                        您还没有订单，快去<a href="index.html">逛逛</a>吧~
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                `,
                
                // 我的评价页面模板
                'reviews': `
                    <div class="review-content">
                        <div class="review-tabs">
                            <div class="tab-item active">全部</div>
                            <div class="tab-item">待评论订单</div>
                            <div class="tab-item">已评论</div>
                        </div>
                        <div class="empty-state">
                            <p>您还没有订单，<a href="index.html">快去逛逛吧</a>~</p>
                        </div>
                    </div>
                `,
                
                // 退货记录页面模板
                'returns': `
                    <div class="returns-content">
                        <div class="returns-header">
                            <h2>退换货记录</h2>
                            <div class="search-bar">
                                <input type="text" class="search-input" placeholder="订单编号">
                                <button class="search-button">查询</button>
                            </div>
                        </div>
                        
                        <div class="returns-tabs">
                            <div class="tab-item active">全部记录</div>
                        </div>

                        <table class="returns-table">
                            <thead>
                                <tr>
                                    <th>退换货单号</th>
                                    <th>订单号</th>
                                    <th>商品信息</th>
                                    <th>申请时间</th>
                                    <th>原因</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="7">
                                        <div class="empty-tip">
                                            您还没有需要处理的订单，<a href="index.html">快去逛逛吧</a>~
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                `,
                
                // 消息通知页面模板
                'messages': `
                    <div class="messages-content">
                        <div class="messages-tabs">
                            <div class="tab-item active">全部消息</div>
                            <div class="tab-item">系统消息</div>
                            <div class="tab-item">活动通知</div>
                        </div>
                        
                        <div class="empty-message">
                            <div class="empty-tip">
                                <i class="fas fa-envelope-open"></i>
                                <p>抱歉，您暂时没有要处理的消息！</p>
                            </div>
                        </div>
                    </div>
                `
            };

            // 定义所有页面样式
            const pageStyles = {
                // 退货记录页面样式
                'returns': `
                    .returns-content {
                        padding: 20px;
                    }
                    .returns-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 20px;
                    }
                `,

                // 消息通知页面样式
                'messages': `
                    .messages-content {
                        padding: 20px;
                    }
                    .messages-tabs {
                        display: flex;
                        border-bottom: 2px solid #f0f0f0;
                        margin-bottom: 30px;
                    }
                `
            };

            // 初始化页面事件
            function initPageEvents() {
                // 订单标签切换
                document.querySelectorAll('.order-tabs .tab-item').forEach(tab => {
                    tab.addEventListener('click', () => {
                        document.querySelector('.order-tabs .tab-item.active').classList.remove('active');
                        tab.classList.add('active');
                    });
                });

                // 评价标签切换
                document.querySelectorAll('.review-tabs .tab-item').forEach(tab => {
                    tab.addEventListener('click', () => {
                        document.querySelector('.review-tabs .tab-item.active').classList.remove('active');
                        tab.classList.add('active');
                    });
                });
            }

            // 切换页面内容
            function switchPage(pageName) {
                const mainContent = document.getElementById('mainContent');
                mainContent.innerHTML = pageTemplates[pageName];
                
                // 添加对应页面的样式
                if (pageStyles[pageName]) {
                    // 检查是否已存在该页面的样式
                    const styleId = `style-${pageName}`;
                    if (!document.getElementById(styleId)) {
                        const styleElement = document.createElement('style');
                        styleElement.id = styleId;
                        styleElement.textContent = pageStyles[pageName];
                        document.head.appendChild(styleElement);
                    }
                }
                
                // 初始化页面特定事件
                if (pageName === 'messages') {
                    initMessageEvents();
                }
                
                initPageEvents();
            }

            // 添加消息页面特定的事件处理
            function initMessageEvents() {
                document.querySelectorAll('.messages-tabs .tab-item').forEach(tab => {
                    tab.addEventListener('click', () => {
                        document.querySelector('.messages-tabs .tab-item.active').classList.remove('active');
                        tab.classList.add('active');
                    });
                });
            }

            // 菜单点击事件处理
            document.querySelectorAll('.menu-item').forEach(item => {
                item.addEventListener('click', () => {
                    // 移除所有active类
                    document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                    // 添加active类到当前点击项
                    item.classList.add('active');
                    
                    // 根据点击项切换内容
                    switch(item.textContent) {
                        case '我的订单':
                            switchPage('orders');
                            break;
                        case '虚拟订单':
                            switchPage('orders');
                            break;
                        case '我的评价':
                            switchPage('reviews');
                            break;
                        case '退货管理':
                            switchPage('returns');
                            break;
                        case '消息通知':
                            switchPage('messages');
                            break;
                        // 可以继续添加其他菜单项的处理...
                    }
                });
            });

            initPageEvents();
        });
    </script>
    <script src="js/login.js"></script>
</body>
</html>